# FLUX.1-dev LoRA Training and Inference Guide

## 🚀 Migration from Stable Diffusion 3.5 to FLUX.1-dev

This codebase has been migrated from Stable Diffusion 3.5 Large to FLUX.1-dev for better performance with smaller datasets (like your 18-image dataset).

## 🔍 Why FLUX.1-dev?

FLUX.1-dev offers several advantages over SD3.5 for character consistency training:

### 1. **Better Performance with Fewer Images** ✅
- FLUX.1-dev can be trained effectively with 10-18 images per character
- SD3.5 typically requires 50+ images for good character consistency
- Your current 18-image dataset is perfect for FLUX.1-dev

### 2. **Improved Architecture** ✅
- More efficient transformer architecture
- Better text understanding with CLIP + T5 encoders
- Optimized for character consistency

### 2. **Training vs Inference Mismatch** ❌
- **Training**: Only applies LoRA to transformer layers
- **Inference**: Tries to load LoRA for all components (transformer + text encoders)
- **Result**: LoRA weights are effectively ignored during inference

### 3. **Key Naming Convention Problems** ❌
Your training saves keys like: `transformer_blocks.0.attn.to_q.lora_A`
Diffusers expects: `transformer.transformer_blocks.0.attn.to_q.lora_A`

## 🛠️ Complete Solution

### Step 1: Diagnose Current Issues

First, let's analyze your current setup:

```bash
# Diagnose your training data
python diagnose_lora_issues.py --data_root ./training

# If you have a checkpoint, diagnose it too
python diagnose_lora_issues.py --checkpoint_path ./path/to/your/checkpoint --generate_fix
```

### Step 2: Fix Training Data (if needed)

Check your caption format. Each caption should follow this pattern:
```
a photo of sks character1, [description of the image]
```

If your captions need fixing:
```bash
python fix_captions.py --data_root ./training
```

## 🛠️ Training with FLUX.1-dev

### Step 1: Train Your Model

The updated `lora_training.py` now uses FLUX.1-dev for better character consistency:

```bash
python lora_training.py \
    --data_root ./training \
    --output_dir ./outputs_flux \
    --model_id black-forest-labs/FLUX.1-dev \
    --lora_rank 8 \
    --lora_alpha 32 \
    --learning_rate 5e-6 \
    --num_epochs 15 \
    --batch_size 1
```

### Step 2: Test with FLUX.1-dev Inference

Use the updated inference script for FLUX.1-dev:

```bash
# Test character consistency
python lora_inference_fixed.py \
    --lora_path ./outputs_flux/checkpoint-final \
    --model_id black-forest-labs/FLUX.1-dev \
    --character character1 \
    --output_dir ./test_results_flux

# Or generate single image
python lora_inference_fixed.py \
    --lora_path ./outputs_flux/checkpoint-final \
    --model_id black-forest-labs/FLUX.1-dev \
    --prompt "a photo of sks character1, portrait, high quality" \
    --output_dir ./test_results_flux
```

## 🔧 If You Have Existing Checkpoints

If you already have trained checkpoints that you want to fix without retraining:

### Option 1: Use the Diagnostic Tool
```bash
python diagnose_lora_issues.py --checkpoint_path ./your_checkpoint --generate_fix
# This creates a fix_lora_checkpoint.py script
python fix_lora_checkpoint.py
```

### Option 2: Manual Fix
```bash
python convert_lora_checkpoint.py ./your_checkpoint --output_path ./fixed_checkpoint
```

## 📊 Key Improvements Made

### 1. **Migrated to FLUX.1-dev** (`lora_training.py`)
- ✅ Updated from SD3.5 to FLUX.1-dev architecture
- ✅ Optimized for smaller datasets (18 images)
- ✅ Better character consistency with fewer images
- ✅ Improved text encoder handling (CLIP + T5)

### 2. **Updated Inference Pipeline** (`lora_inference_fixed.py`)
- ✅ FLUX.1-dev pipeline integration
- ✅ Proper LoRA loading for FLUX architecture
- ✅ Optimized guidance scale (3.5 for FLUX.1-dev)
- ✅ Character consistency testing

### 3. **Enhanced Dataset Handling**
- ✅ Optimized tokenization for FLUX.1-dev
- ✅ Better caption processing
- ✅ Improved memory efficiency

## 🎯 Expected Results with FLUX.1-dev

After migrating to FLUX.1-dev, you should see:

1. **Better character consistency** with your 18-image dataset
2. **Faster training** due to more efficient architecture
3. **Improved text understanding** with CLIP + T5 encoders
4. **Higher quality images** with better detail preservation
5. **More stable training** with fewer overfitting issues

## 🔍 Verification Steps

### 1. Check LoRA Loading
You should see messages like:
```
✅ Successfully loaded LoRA with scale 0.XXX
```
Instead of the warning messages you were getting.

### 2. Test Character Consistency
Generate multiple images with the same character:
```bash
python lora_inference_fixed.py \
    --lora_path ./outputs_fixed/checkpoint-final \
    --character character1 \
    --num_tests 5
```

### 3. Compare Before/After
- **Before**: Generic faces, no character consistency
- **After**: Consistent character features across different poses/scenes

## 🚨 Common Pitfalls to Avoid

1. **Don't mix old and new checkpoints** - Use either the fixed training code or fix existing checkpoints
2. **Ensure proper caption format** - Always use `sks character_name` in captions
3. **Use sufficient training data** - At least 15-30 images per character
4. **Monitor LoRA loading messages** - No warnings should appear during inference

## 📈 Advanced Optimization

For even better results, consider:

1. **Increase LoRA rank** to 16-32 for complex characters
2. **Adjust LoRA alpha** for stronger/weaker adaptation
3. **Use more training epochs** (20-30) for better convergence
4. **Add regularization** with diverse training images

## 🆘 Troubleshooting

If you still have issues:

1. **Run the diagnostic tool** to identify specific problems
2. **Check the console output** for detailed error messages
3. **Verify file paths** and checkpoint structure
4. **Test with a simple prompt** first before complex scenes

The key insight is that your LoRA weights were being saved but not properly loaded during inference due to naming convention mismatches. The fixes ensure proper communication between training and inference phases.
