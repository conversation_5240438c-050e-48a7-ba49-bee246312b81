#!/usr/bin/env python3
"""
Multi-Character LoRA Inference Script for Stable Diffusion 3.5
Load trained LoRA weights and generate images
"""

import json
import torch
from diffusers import FluxPipeline
from PIL import Image
from pathlib import Path
import argparse
from typing import Dict, List, Optional
import traceback

class LoRAInference:
    """Load and use trained LoRA models for FLUX.1-dev inference"""

    def __init__(self, model_id: str, lora_path: str, device: str = "cuda"):
        self.device = device
        self.model_id = model_id
        self.lora_path = Path(lora_path)

        # Load base pipeline
        self.setup_pipeline()

        # Load LoRA weights
        self.load_lora_weights()

    def setup_pipeline(self):
        """Setup the base FLUX.1-dev pipeline"""
        print("Loading FLUX.1-dev pipeline...")

        # Use the high-level pipeline for easier inference
        self.pipeline = FluxPipeline.from_pretrained(
            self.model_id,
            torch_dtype=torch.bfloat16,
            device_map="balanced" if self.device == "cuda" else None
        )

        if self.device != "cuda":
            self.pipeline = self.pipeline.to(self.device)

        print("FLUX.1-dev pipeline loaded successfully!")
        
    def load_lora_weights(self):
        """Load LoRA weights with multiple fallback methods"""
        print("🔄 Loading LoRA weights...")

        # Method 1: Try Diffusers pipeline loading
        try:
            self.pipeline.load_lora_weights(self.lora_path)
            print("✅ Loaded LoRA weights using pipeline method")
            return
        except Exception as e:
            print(f"❌ Pipeline LoRA loading failed: {e}")
            print("🔄 Trying manual loading methods...")

        # Try different manual loading approaches
        success = False

        # Method 2: Try loading pytorch_lora_weights.bin
        try:
            lora_file = Path(self.lora_path) / "pytorch_lora_weights.bin"
            if lora_file.exists():
                print(f"📁 Found {lora_file}")
                lora_weights = torch.load(lora_file, map_location=self.device)
                self.load_config()
                self.apply_lora_to_transformer(lora_weights)
                print("✅ Loaded LoRA weights from pytorch_lora_weights.bin")
                success = True
            else:
                print(f"❌ {lora_file} not found")
        except Exception as e:
            print(f"❌ Failed to load pytorch_lora_weights.bin: {e}")

        # Method 3: Try loading pytorch_lora_weights.safetensors
        if not success:
            try:
                lora_file = Path(self.lora_path) / "pytorch_lora_weights.safetensors"
                if lora_file.exists():
                    print(f"📁 Found {lora_file}")
                    from safetensors.torch import load_file
                    lora_weights = load_file(lora_file, device=self.device)
                    self.load_config()
                    self.apply_lora_to_transformer(lora_weights)
                    print("✅ Loaded LoRA weights from pytorch_lora_weights.safetensors")
                    success = True
                else:
                    print(f"❌ {lora_file} not found")
            except Exception as e:
                print(f"❌ Failed to load pytorch_lora_weights.safetensors: {e}")

        # Method 4: Try loading our custom lora_weights.pt
        if not success:
            try:
                lora_file = Path(self.lora_path) / "lora_weights.pt"
                if lora_file.exists():
                    print(f"📁 Found {lora_file}")
                    lora_weights = torch.load(lora_file, map_location=self.device)
                    self.load_config()
                    self.apply_lora_to_transformer(lora_weights)
                    print("✅ Loaded LoRA weights from lora_weights.pt")
                    success = True
                else:
                    print(f"❌ {lora_file} not found")
            except Exception as e:
                print(f"❌ Failed to load lora_weights.pt: {e}")

        if not success:
            print("❌ All LoRA loading methods failed!")
            print(f"📁 Available files in {self.lora_path}:")
            lora_dir = Path(self.lora_path)
            if lora_dir.exists():
                for file in lora_dir.iterdir():
                    print(f"  - {file.name}")
            else:
                print(f"  Directory {self.lora_path} does not exist!")
            raise RuntimeError("Could not load LoRA weights with any method")

    def load_config(self):
        """Load LoRA configuration"""
        try:
            config_file = Path(self.lora_path) / "config.json"
            if config_file.exists():
                with open(config_file, "r") as f:
                    self.lora_config = json.load(f)
                print(f"📋 Loaded config: rank={self.lora_config.get('lora_rank', 4)}, alpha={self.lora_config.get('lora_alpha', 32)}")
            else:
                print("⚠️  No config.json found, using default values")
                self.lora_config = {"lora_rank": 4, "lora_alpha": 32}
        except Exception as e:
            print(f"⚠️  Error loading config: {e}, using defaults")
            self.lora_config = {"lora_rank": 4, "lora_alpha": 32}
        
    def apply_lora_to_transformer(self, lora_weights: Dict):
        """Apply LoRA weights to transformer layers"""
        # Group weights by layer
        lora_layers = {}
        for key, weight in lora_weights.items():
            layer_name = key.rsplit('.', 1)[0]  # Remove .lora_A or .lora_B
            if layer_name not in lora_layers:
                lora_layers[layer_name] = {}

            if key.endswith('.lora_A'):
                lora_layers[layer_name]['A'] = weight
            elif key.endswith('.lora_B'):
                lora_layers[layer_name]['B'] = weight

        print(f"Applying LoRA to {len(lora_layers)} layers...")

        # Apply LoRA to each layer
        applied_count = 0
        for layer_name, matrices in lora_layers.items():
            if 'A' in matrices and 'B' in matrices:
                try:
                    # Find the corresponding module in the transformer
                    module = self.pipeline.transformer
                    for attr in layer_name.split('.'):
                        module = getattr(module, attr)

                    # Create LoRA adaptation
                    lora_A = matrices['A'].to(self.device)
                    lora_B = matrices['B'].to(self.device)

                    # Calculate LoRA delta weight
                    alpha = self.lora_config.get('lora_alpha', 32)
                    rank = self.lora_config.get('lora_rank', 4)
                    scaling = alpha / rank

                    delta_weight = (lora_B @ lora_A) * scaling

                    # Add to original weight
                    if hasattr(module, 'weight'):
                        # Ensure dtype compatibility
                        delta_weight = delta_weight.to(dtype=module.weight.dtype)
                        module.weight.data += delta_weight
                        applied_count += 1

                except Exception as e:
                    print(f"Warning: Could not apply LoRA to layer {layer_name}: {e}")

        print(f"Successfully applied LoRA to {applied_count} layers")
    
    # Text encoding is now handled by the pipeline internally
    
    def generate_image(
        self,
        prompt: str,
        negative_prompt: str = "blurry, low quality, distorted",
        width: int = 1024,
        height: int = 1024,
        num_inference_steps: int = 28,  # SD3.5 works well with fewer steps
        guidance_scale: float = 7.0,
        seed: Optional[int] = None
    ) -> Image.Image:
        """Generate image from prompt using SD3.5 pipeline"""

        # Set up generator for reproducibility
        generator = None
        if seed is not None:
            generator = torch.Generator(device=self.device).manual_seed(seed)

        # Generate image using the pipeline
        with torch.no_grad():
            result = self.pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                generator=generator,
                return_dict=True
            )

        return result.images[0]
    
    def batch_generate(
        self, 
        prompts: List[str],
        output_dir: str,
        **generation_kwargs
    ):
        """Generate multiple images from a list of prompts"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for i, prompt in enumerate(prompts):
            print(f"Generating image {i+1}/{len(prompts)}: {prompt[:50]}...")
            
            try:
                image = self.generate_image(prompt, **generation_kwargs)
                
                # Save image
                filename = f"generated_{i:03d}.png"
                image.save(output_path / filename)
                
                # Save prompt
                with open(output_path / f"prompt_{i:03d}.txt", "w") as f:
                    f.write(prompt)
                
                print(f"Saved: {filename}")
                
            except Exception as e:
                print(f"Error generating image {i+1}: {e}")
    
    def test_characters(self, characters: List[str], output_dir: str):
        """Test the model with different character prompts"""
        test_prompts = []
        
        base_scenes = [
            "portrait, high quality, detailed",
            "full body, standing, neutral background",
            "close-up face, dramatic lighting",
            "action pose, dynamic scene",
            "sitting, casual pose, indoor setting"
        ]
        
        for char in characters:
            for scene in base_scenes:
                # Use the same format as training: "sks character_name"
                prompt = f"a photo of sks {char}, {scene}"
                test_prompts.append(prompt)
        
        print(f"Testing {len(characters)} characters with {len(base_scenes)} scenes each")
        self.batch_generate(test_prompts, output_dir)

def main():
    parser = argparse.ArgumentParser(description="Generate images with trained LoRA")
    parser.add_argument("--lora_path", type=str, required=True, help="Path to LoRA checkpoint")
    parser.add_argument("--model_id", type=str, default="stabilityai/stable-diffusion-3.5-large")
    parser.add_argument("--prompt", type=str, help="Single prompt to generate")
    parser.add_argument("--prompts_file", type=str, help="File with multiple prompts")
    parser.add_argument("--test_characters", type=str, nargs="+", help="Character names to test")
    parser.add_argument("--output_dir", type=str, default="./generated_images")
    parser.add_argument("--num_images", type=int, default=1, help="Number of images per prompt")
    parser.add_argument("--steps", type=int, default=28)
    parser.add_argument("--guidance_scale", type=float, default=7.0)
    parser.add_argument("--width", type=int, default=1024)
    parser.add_argument("--height", type=int, default=1024)
    parser.add_argument("--seed", type=int, help="Random seed")
    parser.add_argument("--device", type=str, default="cuda")
    
    args = parser.parse_args()
    
    # Initialize inference
    inference = LoRAInference(args.model_id, args.lora_path, args.device)
    
    generation_kwargs = {
        "width": args.width,
        "height": args.height,
        "num_inference_steps": args.steps,
        "guidance_scale": args.guidance_scale,
        "seed": args.seed
    }
    
    if args.test_characters:
        # Test mode with character names
        inference.test_characters(args.test_characters, args.output_dir)
        
    elif args.prompt:
        # Single prompt
        print(f"Generating from prompt: {args.prompt}")
        
        for i in range(args.num_images):
            image = inference.generate_image(args.prompt, **generation_kwargs)
            
            output_path = Path(args.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            filename = f"generated_{i:03d}.png"
            image.save(output_path / filename)
            print(f"Saved: {filename}")
    
    elif args.prompts_file:
        # Multiple prompts from file
        with open(args.prompts_file, 'r') as f:
            prompts = [line.strip() for line in f if line.strip()]
        
        inference.batch_generate(prompts, args.output_dir, **generation_kwargs)
    
    else:
        print("Please provide either --prompt, --prompts_file, or --test_characters")

if __name__ == "__main__":
    main()