#!/usr/bin/env python3
"""
Setup script to help with FLUX.1-dev access and verification
"""

import subprocess
import sys
from pathlib import Path

def check_huggingface_cli():
    """Check if huggingface-cli is installed"""
    try:
        result = subprocess.run(['huggingface-cli', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ huggingface-cli is installed")
            return True
        else:
            print("❌ huggingface-cli not found")
            return False
    except FileNotFoundError:
        print("❌ huggingface-cli not found")
        return False

def install_huggingface_hub():
    """Install huggingface-hub if not present"""
    print("📦 Installing huggingface-hub...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'huggingface-hub'], 
                      check=True)
        print("✅ huggingface-hub installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install huggingface-hub")
        return False

def check_flux_access():
    """Check if user has access to FLUX.1-dev"""
    try:
        from huggingface_hub import HfApi
        api = HfApi()
        
        # Try to get model info
        model_info = api.model_info("black-forest-labs/FLUX.1-dev")
        print("✅ FLUX.1-dev access confirmed!")
        return True
        
    except Exception as e:
        if "gated" in str(e).lower() or "access" in str(e).lower():
            print("❌ FLUX.1-dev access required")
            print("   Please visit: https://huggingface.co/black-forest-labs/FLUX.1-dev")
            print("   Click 'Request Access' and wait for approval")
            return False
        else:
            print(f"❌ Error checking access: {e}")
            return False

def login_huggingface():
    """Guide user through HuggingFace login"""
    print("\n🔑 HuggingFace Login Required")
    print("=" * 40)
    print("1. Go to: https://huggingface.co/settings/tokens")
    print("2. Create a new token (read access is sufficient)")
    print("3. Copy the token")
    print("4. Run: huggingface-cli login")
    print("5. Paste your token when prompted")
    print("\nAlternatively, you can set the HF_TOKEN environment variable:")
    print("export HF_TOKEN=your_token_here")

def main():
    """Main setup function"""
    print("🚀 FLUX.1-dev Access Setup")
    print("=" * 30)
    
    # Check if huggingface-cli is available
    if not check_huggingface_cli():
        print("\n📦 Installing huggingface-hub...")
        if not install_huggingface_hub():
            print("❌ Setup failed. Please install manually:")
            print("   pip install --upgrade huggingface-hub")
            return
    
    # Check current login status
    try:
        result = subprocess.run(['huggingface-cli', 'whoami'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            username = result.stdout.strip()
            print(f"✅ Logged in as: {username}")
        else:
            print("❌ Not logged in to HuggingFace")
            login_huggingface()
            return
    except:
        print("❌ Could not check login status")
        login_huggingface()
        return
    
    # Check FLUX.1-dev access
    print("\n🔍 Checking FLUX.1-dev access...")
    if check_flux_access():
        print("\n🎉 Setup complete! You're ready to use FLUX.1-dev")
        print("\n💡 Next steps:")
        print("1. Run: python test_flux_migration.py")
        print("2. Start training: python lora_training.py --data_root ./training --output_dir ./outputs_flux")
    else:
        print("\n⚠️ Setup incomplete. Please:")
        print("1. Request access to FLUX.1-dev")
        print("2. Wait for approval (check your email)")
        print("3. Run this script again")

if __name__ == "__main__":
    main()
