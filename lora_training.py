#!/usr/bin/env python3
"""
Stable Diffusion 3.5 Large LoRA Fine-Tuning Pipeline
Optimized for character consistency based on best practices
"""

import os
import json
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from transformers import CLIPTextModel, CLIPTokenizer, CLIPTextModelWithProjection, T5EncoderModel, T5TokenizerFast
from diffusers import SD3Transformer2DModel, AutoencoderKL, FlowMatchEulerDiscreteScheduler
from diffusers.optimization import get_scheduler
from diffusers.utils import check_min_version
from PIL import Image
import numpy as np
from pathlib import Path
import argparse
from tqdm import tqdm
import math
from typing import Dict
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import set_seed

# Check diffusers version
check_min_version("0.30.0")

# Setup logging
logger = get_logger(__name__)

# Set memory optimization environment variables
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

class LoRALinearLayer(torch.nn.Module):
    """
    Optimized LoRA Linear Layer implementation
    Based on best practices for character consistency
    """
    def __init__(self, in_features: int, out_features: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank

        # Initialize LoRA matrices with better initialization
        # Use normal distribution for A and zeros for B (standard practice)
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_features) / math.sqrt(rank))
        self.lora_B = torch.nn.Parameter(torch.zeros(out_features, rank))

    def forward(self, x):
        # Ensure consistent dtype and device
        lora_A = self.lora_A.to(x.dtype).to(x.device)
        lora_B = self.lora_B.to(x.dtype).to(x.device)

        # Compute LoRA output: x @ A^T @ B^T * scaling
        return (x @ lora_A.T @ lora_B.T) * self.scaling

class LoRAConv2d(torch.nn.Module):
    """LoRA Convolutional Layer implementation"""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        self.kernel_size = kernel_size
        
        # LoRA matrices for conv layers
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_channels, kernel_size, kernel_size) * 0.01)
        self.lora_B = torch.nn.Parameter(torch.zeros(out_channels, rank, 1, 1))
        
    def forward(self, x):
        # Ensure consistent dtype
        lora_A = self.lora_A.to(x.dtype)
        lora_B = self.lora_B.to(x.dtype)
        return F.conv2d(x, (lora_B.squeeze() @ lora_A.view(self.rank, -1)).view(
            lora_B.shape[0], lora_A.shape[1], self.kernel_size, self.kernel_size
        ), padding=self.kernel_size//2) * self.scaling

class MultiCharacterDataset(Dataset):
    """Dataset for multi-character training with SD3.5"""

    def __init__(self, data_root: str, tokenizer: CLIPTokenizer, tokenizer_2: CLIPTokenizer, tokenizer_3: T5TokenizerFast, size: int = 1024):
        self.data_root = Path(data_root)
        self.tokenizer = tokenizer
        self.tokenizer_2 = tokenizer_2
        self.tokenizer_3 = tokenizer_3
        self.size = size
        self.images = []
        self.captions = []
        
        # Load data from character folders
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]

        for char_folder in self.data_root.iterdir():
            if char_folder.is_dir():
                char_name = char_folder.name
                print(f"Processing character folder: {char_name}")

                # Load images and captions for all supported formats
                char_images_count = 0
                for ext in image_extensions:
                    for img_file in char_folder.glob(ext):
                        # Look for corresponding caption file
                        caption_file = img_file.with_suffix(".txt")
                        if caption_file.exists():
                            try:
                                with open(caption_file, 'r', encoding='utf-8') as f:
                                    caption = f.read().strip()

                                # Add character token to caption with more specific formatting
                                # Use a special token format that's more distinctive
                                caption = f"a photo of sks {char_name}, {caption}"

                                self.images.append(str(img_file))
                                self.captions.append(caption)
                                char_images_count += 1
                            except Exception as e:
                                print(f"Warning: Could not read caption file {caption_file}: {e}")
                        else:
                            print(f"Warning: No caption file found for {img_file}")

                print(f"  Found {char_images_count} images with captions for {char_name}")

        print(f"Loaded {len(self.images)} images from {len(list(self.data_root.iterdir()))} characters")

        # Check if dataset is empty
        if len(self.images) == 0:
            raise ValueError(
                f"No images found in {self.data_root}. "
                f"Make sure your data structure is: data_root/character_name/image.ext and data_root/character_name/image.txt "
                f"Supported image formats: {', '.join(image_extensions)}"
            )
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        # Load and preprocess image
        image_path = self.images[idx]
        image = Image.open(image_path).convert("RGB")
        
        # Resize and center crop
        image = image.resize((self.size, self.size), Image.LANCZOS)
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image).permute(2, 0, 1)
        
        # Normalize to [-1, 1]
        image = (image - 0.5) / 0.5
        
        # Tokenize caption for all three text encoders
        caption = self.captions[idx]

        # CLIP tokenizer 1
        text_inputs = self.tokenizer(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        # CLIP tokenizer 2
        text_inputs_2 = self.tokenizer_2(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        # T5 tokenizer
        text_inputs_3 = self.tokenizer_3(
            caption,
            padding="max_length",
            max_length=256,  # T5 uses longer sequences
            truncation=True,
            return_tensors="pt"
        )

        return {
            "pixel_values": image,
            "input_ids": text_inputs.input_ids.squeeze(),
            "attention_mask": text_inputs.attention_mask.squeeze(),
            "input_ids_2": text_inputs_2.input_ids.squeeze(),
            "attention_mask_2": text_inputs_2.attention_mask.squeeze(),
            "input_ids_3": text_inputs_3.input_ids.squeeze(),
            "attention_mask_3": text_inputs_3.attention_mask.squeeze()
        }

class LoRATrainer:
    """Multi-Character LoRA Trainer for SD3.5"""

    def __init__(self, config: Dict):
        self.config = config
        self.accelerator = Accelerator(
            gradient_accumulation_steps=config.get("gradient_accumulation_steps", 1),
            mixed_precision=config.get("mixed_precision", "fp16")
        )

        # Set random seed
        if config.get("seed"):
            set_seed(config["seed"])

        self.setup_models()
        self.setup_lora_layers()
        self.verify_dimensions()
        
    def setup_models(self):
        """Initialize SD3.5 models"""
        model_id = self.config.get("model_id", "stabilityai/stable-diffusion-3.5-large")

        # Determine dtype based on mixed precision setting - use bfloat16 for better memory efficiency
        if self.config.get("mixed_precision") == "no":
            model_dtype = torch.float32
        elif self.config.get("mixed_precision") == "bf16":
            model_dtype = torch.bfloat16
        else:
            model_dtype = torch.float16

        # Load tokenizers (SD3.5 has three)
        self.tokenizer = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer"
        )
        self.tokenizer_2 = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer_2"
        )
        self.tokenizer_3 = T5TokenizerFast.from_pretrained(
            model_id, subfolder="tokenizer_3"
        )

        # Load text encoders (SD3.5 has three) with memory optimization
        self.text_encoder = CLIPTextModel.from_pretrained(
            model_id, subfolder="text_encoder", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )
        self.text_encoder_2 = CLIPTextModelWithProjection.from_pretrained(
            model_id, subfolder="text_encoder_2", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )
        self.text_encoder_3 = T5EncoderModel.from_pretrained(
            model_id, subfolder="text_encoder_3", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load Transformer (MMDiT) instead of UNet with memory optimization
        self.transformer = SD3Transformer2DModel.from_pretrained(
            model_id, subfolder="transformer", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load VAE with memory optimization
        self.vae = AutoencoderKL.from_pretrained(
            model_id, subfolder="vae", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load scheduler
        self.scheduler = FlowMatchEulerDiscreteScheduler.from_pretrained(
            model_id, subfolder="scheduler"
        )

        # Freeze original parameters and enable memory efficient attention
        self.text_encoder.requires_grad_(False)
        self.text_encoder_2.requires_grad_(False)
        self.text_encoder_3.requires_grad_(False)
        self.transformer.requires_grad_(False)
        self.vae.requires_grad_(False)

        # Enable memory efficient attention if available
        if hasattr(self.transformer, 'enable_xformers_memory_efficient_attention'):
            try:
                self.transformer.enable_xformers_memory_efficient_attention()
                logger.info("Enabled xformers memory efficient attention")
            except Exception as e:
                logger.warning(f"Could not enable xformers: {e}")

        # Enable gradient checkpointing for memory savings
        if hasattr(self.transformer, 'enable_gradient_checkpointing'):
            self.transformer.enable_gradient_checkpointing()
            logger.info("Enabled gradient checkpointing")
        
    def setup_lora_layers(self):
        """
        Setup LoRA layers for SD3.5 Transformer
        Optimized for character consistency based on best practices
        """
        self.lora_layers = {}
        rank = self.config.get("lora_rank", 8)  # Higher rank for better character learning
        alpha = self.config.get("lora_alpha", 32)

        logger.info(f"Setting up LoRA layers with rank={rank}, alpha={alpha}")
        lora_target_count = 0

        # Target specific layers that are most important for character consistency
        # Based on the Medium tutorial's emphasis on attention layers
        target_modules = [
            "attn.to_q", "attn.to_k", "attn.to_v", "attn.to_out.0",  # Self-attention
            "norm1", "norm2",  # Layer normalization (important for stability)
            "ff.net.0", "ff.net.2"  # Feed-forward layers
        ]

        for name, module in self.transformer.named_modules():
            # More selective LoRA application focusing on key layers
            should_add_lora = any(target in name for target in target_modules) and isinstance(module, torch.nn.Linear)

            if should_add_lora:
                # Skip if already has LoRA
                if hasattr(module, 'lora_A'):
                    continue

                lora_target_count += 1
                if lora_target_count <= 10:  # Print more for debugging
                    logger.info(f"  Adding LoRA to: {name} ({type(module).__name__})")

                # Create LoRA layer with same dtype as the module
                lora_layer = LoRALinearLayer(
                    module.in_features,
                    module.out_features,
                    rank=rank,
                    alpha=alpha
                )

                # Convert LoRA layer to same dtype and device as the original module
                lora_layer = lora_layer.to(dtype=module.weight.dtype, device=module.weight.device)

                # Store LoRA layer
                self.lora_layers[name] = lora_layer

                # Hook to add LoRA output to original - improved version
                def make_hook(lora_layer, module_name):
                    def hook(_, input, output):
                        try:
                            # Ensure input and output have consistent dtype
                            input_tensor = input[0]
                            lora_output = lora_layer(input_tensor)
                            # Ensure lora_output matches output dtype and device
                            lora_output = lora_output.to(dtype=output.dtype, device=output.device)
                            return output + lora_output
                        except Exception as e:
                            logger.warning(f"LoRA hook failed for {module_name}: {e}")
                            return output
                    return hook

                module.register_forward_hook(make_hook(lora_layer, name))

        # Collect trainable parameters
        self.trainable_params = []
        for lora_layer in self.lora_layers.values():
            self.trainable_params.extend(lora_layer.parameters())

        logger.info(f"Added LoRA to {len(self.lora_layers)} layers")
        logger.info(f"Trainable parameters: {sum(p.numel() for p in self.trainable_params):,}")

        # Calculate LoRA efficiency
        total_params = sum(p.numel() for p in self.transformer.parameters())
        lora_params = sum(p.numel() for p in self.trainable_params)
        efficiency = (lora_params / total_params) * 100
        logger.info(f"LoRA efficiency: {efficiency:.2f}% of total parameters")

    def encode_prompt(self, input_ids, input_ids_2, input_ids_3, attention_mask, attention_mask_2, attention_mask_3):
        """Encode prompts using all three SD3.5 text encoders"""
        # Encode with first text encoder (CLIP L/14)
        text_encoder_output = self.text_encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )
        prompt_embeds_1 = text_encoder_output.last_hidden_state

        # Encode with second text encoder (OpenCLIP bigG/14)
        text_encoder_2_output = self.text_encoder_2(
            input_ids=input_ids_2,
            attention_mask=attention_mask_2,
            return_dict=True
        )
        prompt_embeds_2 = text_encoder_2_output.last_hidden_state
        pooled_prompt_embeds_2 = text_encoder_2_output.text_embeds

        # Encode with third text encoder (T5-XXL)
        text_encoder_3_output = self.text_encoder_3(
            input_ids=input_ids_3,
            attention_mask=attention_mask_3,
            return_dict=True
        )
        prompt_embeds_3 = text_encoder_3_output.last_hidden_state

        # SD3 specific embedding handling
        # Based on the official SD3 implementation, we need to handle embeddings differently

        # Get dimensions
        batch_size = prompt_embeds_1.shape[0]
        clip_seq_len = 77  # CLIP sequence length
        t5_seq_len = 256   # T5 sequence length

        # Concatenate CLIP embeddings along feature dimension
        clip_prompt_embeds = torch.cat([prompt_embeds_1, prompt_embeds_2], dim=-1)

        # Check if we should use CLIP only (fallback for dimension issues)
        if self.config.get("clip_only", False):
            # Use only CLIP embeddings to avoid dimension mismatch issues
            encoder_hidden_states = clip_prompt_embeds
        else:
            # For SD3, we concatenate along sequence dimension, not feature dimension
            # This means we stack CLIP and T5 embeddings as separate sequences

            # Ensure both have the same feature dimension by padding if necessary
            clip_feat_dim = clip_prompt_embeds.shape[-1]
            t5_feat_dim = prompt_embeds_3.shape[-1]

            if clip_feat_dim != t5_feat_dim:
                # Pad the smaller one to match the larger one
                if clip_feat_dim < t5_feat_dim:
                    # Pad CLIP embeddings
                    padding = torch.zeros(
                        batch_size, clip_seq_len, t5_feat_dim - clip_feat_dim,
                        device=clip_prompt_embeds.device, dtype=clip_prompt_embeds.dtype
                    )
                    clip_prompt_embeds = torch.cat([clip_prompt_embeds, padding], dim=-1)
                else:
                    # Pad T5 embeddings
                    padding = torch.zeros(
                        batch_size, t5_seq_len, clip_feat_dim - t5_feat_dim,
                        device=prompt_embeds_3.device, dtype=prompt_embeds_3.dtype
                    )
                    prompt_embeds_3 = torch.cat([prompt_embeds_3, padding], dim=-1)

            # Now concatenate along sequence dimension (dim=1)
            # This creates [batch_size, clip_seq_len + t5_seq_len, feature_dim]
            encoder_hidden_states = torch.cat([clip_prompt_embeds, prompt_embeds_3], dim=1)

        # Fix pooled projections dimension for SD3
        # Based on the error, the transformer's text_embedder.linear_1 expects 2048 input features
        # but we're providing 1280 (from CLIP 2 text_embeds)

        # The correct approach for SD3 is to concatenate pooled embeddings from both CLIP encoders
        # CLIP 1: Use the CLS token (first token) from the last hidden state
        pooled_prompt_embeds_1 = prompt_embeds_1[:, 0, :]  # [batch_size, 768]

        # CLIP 2: Use the text_embeds (already pooled)
        # pooled_prompt_embeds_2 is already [batch_size, 1280]

        # Concatenate both pooled embeddings: 768 + 1280 = 2048
        pooled_projections = torch.cat([pooled_prompt_embeds_1, pooled_prompt_embeds_2], dim=-1)

        # Verify dimension
        expected_dim = 2048
        if pooled_projections.shape[-1] != expected_dim:
            logger.warning(f"Pooled projections dimension mismatch: got {pooled_projections.shape[-1]}, expected {expected_dim}")

            # Adjust dimension if needed
            current_dim = pooled_projections.shape[-1]
            if current_dim < expected_dim:
                # Pad with zeros
                padding_dim = expected_dim - current_dim
                padding = torch.zeros(
                    pooled_projections.shape[0], padding_dim,
                    device=pooled_projections.device, dtype=pooled_projections.dtype
                )
                pooled_projections = torch.cat([pooled_projections, padding], dim=-1)
            elif current_dim > expected_dim:
                # Truncate
                pooled_projections = pooled_projections[:, :expected_dim]

        return encoder_hidden_states, pooled_projections

    def verify_dimensions(self):
        """Verify that our tensor dimensions match what the transformer expects"""
        logger.info("🔍 Verifying tensor dimensions...")

        try:
            # Check what the transformer expects for pooled projections
            if hasattr(self.transformer, 'time_text_embed'):
                time_text_embed = self.transformer.time_text_embed
                if hasattr(time_text_embed, 'text_embedder'):
                    text_embedder = time_text_embed.text_embedder
                    if hasattr(text_embedder, 'linear_1'):
                        expected_pooled_dim = text_embedder.linear_1.in_features
                        logger.info(f"  Expected pooled projection dimension: {expected_pooled_dim}")

                        # Check our text encoder dimensions
                        clip1_dim = self.text_encoder.config.hidden_size
                        clip2_dim = self.text_encoder_2.config.projection_dim
                        our_combined_dim = clip1_dim + clip2_dim

                        logger.info(f"  CLIP 1 dimension: {clip1_dim}")
                        logger.info(f"  CLIP 2 dimension: {clip2_dim}")
                        logger.info(f"  Our combined dimension: {our_combined_dim}")

                        if our_combined_dim == expected_pooled_dim:
                            logger.info("  ✅ Dimensions match!")
                        else:
                            logger.warning(f"  ⚠️ Dimension mismatch! Expected {expected_pooled_dim}, got {our_combined_dim}")

            # Check encoder hidden states dimension
            if hasattr(self.transformer.config, 'joint_attention_dim'):
                expected_encoder_dim = self.transformer.config.joint_attention_dim
                logger.info(f"  Expected encoder hidden states dimension: {expected_encoder_dim}")

        except Exception as e:
            logger.warning(f"Could not verify dimensions: {e}")

        logger.info("Dimension verification complete.")

    def train(self, train_dataloader):
        """
        Optimized training loop based on Medium tutorial best practices
        """
        # Setup optimizer with better parameters for character consistency
        optimizer = torch.optim.AdamW(
            self.trainable_params,
            lr=self.config.get("learning_rate", 5e-6),  # Lower LR as recommended in tutorial
            betas=(0.9, 0.999),
            weight_decay=self.config.get("weight_decay", 0.01),
            eps=1e-08
        )

        # Setup cosine scheduler with warmup (tutorial recommendation)
        num_training_steps = len(train_dataloader) * self.config.get("num_epochs", 10)
        warmup_steps = min(self.config.get("warmup_steps", 100), num_training_steps // 10)

        lr_scheduler = get_scheduler(
            "cosine_with_restarts",  # Better for fine-tuning
            optimizer=optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps
        )
        
        # Prepare for distributed training
        lora_modules = list(self.lora_layers.values())
        optimizer, train_dataloader, lr_scheduler = self.accelerator.prepare(
            optimizer, train_dataloader, lr_scheduler
        )
        
        # Move models to device
        self.transformer.to(self.accelerator.device)
        self.text_encoder.to(self.accelerator.device)
        self.text_encoder_2.to(self.accelerator.device)
        self.text_encoder_3.to(self.accelerator.device)
        self.vae.to(self.accelerator.device)
        
        for lora_layer in lora_modules:
            lora_layer.to(self.accelerator.device)
        
        # Training loop
        global_step = 0
        
        for epoch in range(self.config.get("num_epochs", 10)):
            for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1}"):
                with self.accelerator.accumulate(self.transformer):
                    # Encode images to latent space with memory optimization
                    pixel_values = batch["pixel_values"].to(self.accelerator.device, dtype=self.vae.dtype)

                    # Use no_grad for VAE encoding to save memory
                    with torch.no_grad():
                        latents = self.vae.encode(pixel_values).latent_dist.sample()
                        latents = latents * self.vae.config.scaling_factor

                    # Ensure latents require grad for training
                    latents = latents.detach().requires_grad_()

                    # Sample noise with same dtype as latents
                    noise = torch.randn_like(latents, dtype=latents.dtype, device=latents.device)
                    bsz = latents.shape[0]

                    # Sample timesteps for flow matching (0 to 1 instead of 0 to 1000)
                    timesteps = torch.rand((bsz,), device=latents.device, dtype=latents.dtype)

                    # Flow matching: interpolate between noise and data
                    # x_t = (1 - t) * noise + t * latents
                    noisy_latents = (1 - timesteps.view(-1, 1, 1, 1)) * noise + timesteps.view(-1, 1, 1, 1) * latents

                    # Encode text with all three encoders
                    input_ids = batch["input_ids"].to(self.accelerator.device)
                    input_ids_2 = batch["input_ids_2"].to(self.accelerator.device)
                    input_ids_3 = batch["input_ids_3"].to(self.accelerator.device)
                    attention_mask = batch["attention_mask"].to(self.accelerator.device)
                    attention_mask_2 = batch["attention_mask_2"].to(self.accelerator.device)
                    attention_mask_3 = batch["attention_mask_3"].to(self.accelerator.device)

                    # Encode text with no_grad to save memory, then detach and require grad
                    with torch.no_grad():
                        encoder_hidden_states, pooled_prompt_embeds = self.encode_prompt(
                            input_ids, input_ids_2, input_ids_3, attention_mask, attention_mask_2, attention_mask_3
                        )

                    # Detach and require grad for training
                    encoder_hidden_states = encoder_hidden_states.detach().requires_grad_()
                    pooled_prompt_embeds = pooled_prompt_embeds.detach().requires_grad_()

                    # Ensure embeddings have correct dtype
                    encoder_hidden_states = encoder_hidden_states.to(dtype=noisy_latents.dtype)
                    pooled_prompt_embeds = pooled_prompt_embeds.to(dtype=noisy_latents.dtype)

                    # Debug: Print shapes on first iteration to verify dimensions
                    if global_step == 0:
                        logger.info(f"Debug - Tensor shapes:")
                        logger.info(f"  noisy_latents: {noisy_latents.shape}")
                        logger.info(f"  timesteps: {timesteps.shape}")
                        logger.info(f"  encoder_hidden_states: {encoder_hidden_states.shape}")
                        logger.info(f"  pooled_prompt_embeds: {pooled_prompt_embeds.shape}")

                    # Predict the velocity (flow direction)
                    try:
                        # Try the standard SD3 transformer call
                        model_pred = self.transformer(
                            hidden_states=noisy_latents,
                            timestep=timesteps,
                            encoder_hidden_states=encoder_hidden_states,
                            pooled_projections=pooled_prompt_embeds,
                            return_dict=False
                        )[0]
                    except RuntimeError as e:
                        if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                            logger.error(f"Matrix multiplication error in transformer:")
                            logger.error(f"  noisy_latents shape: {noisy_latents.shape}")
                            logger.error(f"  encoder_hidden_states shape: {encoder_hidden_states.shape}")
                            logger.error(f"  pooled_prompt_embeds shape: {pooled_prompt_embeds.shape}")
                            logger.error(f"  timesteps shape: {timesteps.shape}")

                            # Try alternative approaches
                            logger.info("Trying fallback approaches...")

                            # Fallback 1: Use only CLIP embeddings (first 77 tokens)
                            try:
                                clip_only_embeds = encoder_hidden_states[:, :77, :]
                                model_pred = self.transformer(
                                    hidden_states=noisy_latents,
                                    timestep=timesteps,
                                    encoder_hidden_states=clip_only_embeds,
                                    pooled_projections=pooled_prompt_embeds,
                                    return_dict=False
                                )[0]
                                logger.info("Fallback 1 (CLIP-only embeddings) successful!")
                            except Exception as fallback1_e:
                                logger.error(f"Fallback 1 failed: {fallback1_e}")

                                # Fallback 2: Try with only CLIP 2 pooled embeddings
                                try:
                                    # Re-encode to get original CLIP 2 pooled embeddings
                                    text_encoder_2_output = self.text_encoder_2(
                                        input_ids=input_ids_2,
                                        attention_mask=attention_mask_2,
                                        return_dict=True
                                    )
                                    original_pooled = text_encoder_2_output.text_embeds

                                    model_pred = self.transformer(
                                        hidden_states=noisy_latents,
                                        timestep=timesteps,
                                        encoder_hidden_states=clip_only_embeds,
                                        pooled_projections=original_pooled,
                                        return_dict=False
                                    )[0]
                                    logger.info("Fallback 2 (original pooled embeddings) successful!")
                                except Exception as fallback2_e:
                                    logger.error(f"Fallback 2 also failed: {fallback2_e}")
                                    logger.error("All fallback approaches failed. This might be a model compatibility issue.")
                                    raise e
                        else:
                            raise e

                    # Flow matching loss: predict v = data - noise
                    target = latents - noise

                    # Compute loss with better stability (tutorial recommendation)
                    model_pred = model_pred.to(dtype=target.dtype)

                    # Use Huber loss for better stability and character consistency
                    loss = F.smooth_l1_loss(model_pred, target, reduction="mean", beta=0.1)

                    # Add small regularization term to prevent overfitting
                    if self.config.get("use_regularization", True):
                        reg_loss = 0.0
                        for lora_layer in self.lora_layers.values():
                            reg_loss += torch.norm(lora_layer.lora_A) + torch.norm(lora_layer.lora_B)
                        loss += 1e-6 * reg_loss
                    
                    # Backward pass
                    self.accelerator.backward(loss)

                    if self.accelerator.sync_gradients:
                        # Handle gradient clipping properly for FP16 mixed precision
                        if self.config.get("mixed_precision") == "fp16":
                            # For FP16, we need to handle gradient clipping more carefully
                            # to avoid the "Attempting to unscale FP16 gradients" error
                            try:
                                self.accelerator.clip_grad_norm_(self.trainable_params, 1.0)
                            except ValueError as e:
                                if "Attempting to unscale FP16 gradients" in str(e):
                                    # Skip gradient clipping for this step to avoid the error
                                    # This is a known issue with FP16 and gradient scaling
                                    logger.warning("Skipping gradient clipping due to FP16 unscaling issue")
                                    pass
                                else:
                                    raise e
                        else:
                            # For bf16 and fp32, gradient clipping works normally
                            self.accelerator.clip_grad_norm_(self.trainable_params, 1.0)

                    optimizer.step()
                    lr_scheduler.step()
                    optimizer.zero_grad()

                    # Clear cache to free memory
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                if self.accelerator.sync_gradients:
                    global_step += 1

                    # Enhanced logging for better monitoring
                    if global_step % 50 == 0:  # More frequent logging
                        current_lr = lr_scheduler.get_last_lr()[0]
                        logger.info(f"Epoch {epoch+1}/{self.config.get('num_epochs', 10)} | "
                                  f"Step {global_step} | "
                                  f"Loss: {loss.item():.6f} | "
                                  f"LR: {current_lr:.2e}")

                        # Log memory usage if available
                        if torch.cuda.is_available():
                            memory_used = torch.cuda.memory_allocated() / 1024**3
                            memory_total = torch.cuda.memory_reserved() / 1024**3
                            logger.info(f"GPU Memory: {memory_used:.1f}GB / {memory_total:.1f}GB")

                    # Save checkpoint more frequently
                    if global_step % self.config.get("save_steps", 250) == 0:
                        self.save_checkpoint(global_step)
                        logger.info(f"Checkpoint saved at step {global_step}")
        
        # Final save
        self.save_checkpoint("final")
        logger.info("Training completed successfully!")

        # Log final statistics
        total_steps = global_step
        avg_loss = loss.item()  # Last recorded loss
        logger.info(f"Final Statistics:")
        logger.info(f"  Total training steps: {total_steps}")
        logger.info(f"  Final loss: {avg_loss:.6f}")
        logger.info(f"  LoRA parameters trained: {sum(p.numel() for p in self.trainable_params):,}")

    def validate_model(self, validation_prompt="a photo of sks character1, portrait"):
        """
        Simple validation during training (tutorial recommendation)
        """
        try:
            # This is a placeholder for validation logic
            # In a full implementation, you would generate a sample image
            # and compute metrics like CLIP score
            logger.info(f"Validation prompt: {validation_prompt}")
            # For now, just log that validation would happen here
            logger.info("Validation step completed")
        except Exception as e:
            logger.warning(f"Validation failed: {e}")
    
    # Flow matching doesn't need the old noise scheduling method
    # SD3.5 uses rectified flow which is simpler: x_t = (1-t)*noise + t*data
    
    def save_checkpoint(self, step):
        """Save LoRA weights in Diffusers-compatible format"""
        save_dir = Path(self.config["output_dir"]) / f"checkpoint-{step}"
        save_dir.mkdir(parents=True, exist_ok=True)

        # Save LoRA weights in the format expected by Diffusers
        lora_state_dict = {}
        for name, lora_layer in self.lora_layers.items():
            # Convert our layer names to proper Diffusers format
            # Our format: "transformer_blocks.0.attn.to_q"
            # Diffusers format: "transformer.transformer_blocks.0.attn.to_q.lora_A"

            # Ensure the name starts with "transformer." for proper prefix matching
            if not name.startswith("transformer."):
                diffusers_name = f"transformer.{name}"
            else:
                diffusers_name = name

            lora_state_dict[f"{diffusers_name}.lora_A"] = lora_layer.lora_A.data.cpu()
            lora_state_dict[f"{diffusers_name}.lora_B"] = lora_layer.lora_B.data.cpu()

        # Print some keys for debugging
        logger.info(f"Saving {len(lora_state_dict)} LoRA weight tensors")
        sample_keys = list(lora_state_dict.keys())[:5]
        logger.info(f"Sample keys: {sample_keys}")

        # Save in both formats for compatibility
        torch.save(lora_state_dict, save_dir / "lora_weights.pt")  # Our format
        torch.save(lora_state_dict, save_dir / "pytorch_lora_weights.bin")  # Diffusers format

        # Also save as safetensors (preferred format)
        try:
            from safetensors.torch import save_file
            save_file(lora_state_dict, save_dir / "pytorch_lora_weights.safetensors")
            logger.info("Saved safetensors format")
        except ImportError:
            logger.warning("safetensors not available, skipping .safetensors save")

        # Save config with additional metadata
        config_to_save = self.config.copy()
        config_to_save.update({
            "lora_layers_count": len(self.lora_layers),
            "trainable_params": sum(p.numel() for p in self.trainable_params),
            "step": step
        })

        with open(save_dir / "config.json", "w") as f:
            json.dump(config_to_save, f, indent=2)

        logger.info(f"Saved checkpoint to {save_dir}")

def main():
    """
    Main training function with optimizations from Medium tutorial
    """
    # Clear any existing CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"🔍 GPU Memory: {total_memory:.1f}GB total")
        if total_memory < 16:
            logger.warning("⚠️  Low VRAM detected! Consider using --low_vram flag")

        # Log GPU info
        gpu_name = torch.cuda.get_device_name(0)
        logger.info(f"🎮 GPU: {gpu_name}")
    else:
        logger.warning("⚠️  CUDA not available, training will be very slow")

    parser = argparse.ArgumentParser(description="Train multi-character LoRA")
    parser.add_argument("--data_root", type=str, required=True, help="Root directory containing character folders")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for checkpoints")
    parser.add_argument("--model_id", type=str, default="stabilityai/stable-diffusion-3.5-large")
    parser.add_argument("--resolution", type=int, default=1024)
    parser.add_argument("--batch_size", type=int, default=1, help="Batch size (keep at 1 for memory efficiency)")
    parser.add_argument("--num_epochs", type=int, default=15, help="Number of training epochs (tutorial recommends 15+)")
    parser.add_argument("--learning_rate", type=float, default=5e-6, help="Learning rate (tutorial recommends 5e-6)")
    parser.add_argument("--lora_rank", type=int, default=8, help="LoRA rank (8 for good character learning)")
    parser.add_argument("--lora_alpha", type=float, default=32, help="LoRA alpha (32 for balanced adaptation)")
    parser.add_argument("--weight_decay", type=float, default=0.01, help="Weight decay for regularization")
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--mixed_precision", type=str, default="auto", choices=["no", "fp16", "bf16", "auto"],
                        help="Mixed precision mode. 'auto' will use 'no' for MPS, 'fp16' for CUDA. Use 'no' if you encounter dtype errors")
    parser.add_argument("--force_fp32", action="store_true",
                        help="Force all operations to use fp32 (disables mixed precision completely)")
    parser.add_argument("--clip_only", action="store_true",
                        help="Use only CLIP embeddings (skip T5) to avoid dimension issues")
    parser.add_argument("--low_vram", action="store_true",
                        help="Enable aggressive memory optimizations for low VRAM systems")
    parser.add_argument("--cpu_offload", action="store_true",
                        help="Offload models to CPU when not in use (slower but saves VRAM)")
    
    args = parser.parse_args()
    
    # Create optimized config based on tutorial best practices
    config = {
        "data_root": args.data_root,
        "output_dir": args.output_dir,
        "model_id": args.model_id,
        "resolution": args.resolution,
        "batch_size": args.batch_size,
        "num_epochs": args.num_epochs,
        "learning_rate": args.learning_rate,
        "lora_rank": args.lora_rank,
        "lora_alpha": args.lora_alpha,
        "weight_decay": args.weight_decay,
        "seed": args.seed,
        "gradient_accumulation_steps": 8,  # Higher for better stability
        "save_steps": 250,  # Save more frequently
        "warmup_steps": 100,
        "use_regularization": True,  # Enable regularization for better character consistency
        "mixed_precision": (
            "no" if args.force_fp32 or (args.mixed_precision == "auto" and torch.backends.mps.is_available())
            else "bf16" if args.mixed_precision == "auto" and torch.cuda.is_available() and torch.cuda.is_bf16_supported()
            else "fp16" if args.mixed_precision == "auto" and torch.cuda.is_available()
            else "no" if args.mixed_precision == "auto"
            else args.mixed_precision
        ),
        "clip_only": args.clip_only,
        "low_vram": args.low_vram,
        "cpu_offload": args.cpu_offload
    }
    
    # Setup dataset
    tokenizer = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer")
    tokenizer_2 = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer_2")
    tokenizer_3 = T5TokenizerFast.from_pretrained(args.model_id, subfolder="tokenizer_3")

    dataset = MultiCharacterDataset(
        args.data_root,
        tokenizer,
        tokenizer_2,
        tokenizer_3,
        size=args.resolution
    )

    # Enhanced dataset validation (tutorial recommendation)
    if len(dataset) == 0:
        logger.error("❌ Dataset is empty! Please check your data directory structure.")
        logger.error(f"Expected structure: {args.data_root}/character_name/image.ext and {args.data_root}/character_name/image.txt")
        return

    # Log dataset statistics
    logger.info(f"📊 Dataset loaded successfully:")
    logger.info(f"  Total images: {len(dataset)}")

    # Validate dataset size (tutorial recommendation: 15+ images per character)
    if len(dataset) < 15:
        logger.warning(f"⚠️  Small dataset ({len(dataset)} images). Recommended: 15+ for good character consistency")
        logger.warning("Consider adding more training images or reducing epochs to prevent overfitting")
    elif len(dataset) >= 50:
        logger.info("✅ Good dataset size for character consistency training")
    else:
        logger.info("✅ Adequate dataset size, should work well")

    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    # Create trainer and start training
    try:
        logger.info("🚀 Starting LoRA training with optimized settings...")
        trainer = LoRATrainer(config)
        trainer.train(dataloader)

        logger.info("🎉 Training completed successfully!")
        logger.info(f"📁 Checkpoints saved in: {args.output_dir}")
        logger.info("💡 Use the latest checkpoint for inference with lora_inference_fixed.py")

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    main()
