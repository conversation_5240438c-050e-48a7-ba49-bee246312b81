#!/usr/bin/env python3
"""
Fixed LoRA Inference Script for FLUX.1-dev
<PERSON><PERSON><PERSON> handles LoRA weight loading and application
Migrated from Stable Diffusion 3.5 to FLUX.1-dev
"""

import json
import torch
from diffusers import FluxPipeline
from PIL import Image
from pathlib import Path
import argparse
from typing import Dict, List, Optional
import traceback

class FixedLoRAInference:
    """Improved LoRA inference with proper weight loading for FLUX.1-dev"""

    def __init__(self, model_id: str, lora_path: str, device: str = "cuda"):
        self.device = device
        self.model_id = model_id
        self.lora_path = Path(lora_path)
        self.lora_config = {}

        # Load base pipeline
        self.setup_pipeline()

        # Load LoRA weights with improved method
        self.load_lora_weights_fixed()
        
    def setup_pipeline(self):
        """Setup the base FLUX.1-dev pipeline"""
        print("🔄 Loading FLUX.1-dev pipeline...")

        # Use the high-level pipeline for easier inference
        self.pipeline = FluxPipeline.from_pretrained(
            self.model_id,
            torch_dtype=torch.bfloat16,  # FLUX.1-dev works better with bfloat16
            device_map="balanced" if self.device == "cuda" else None,
            use_safetensors=True
        )

        if self.device != "cuda":
            self.pipeline = self.pipeline.to(self.device)

        print("✅ FLUX.1-dev pipeline loaded successfully!")
        
    def load_config(self):
        """Load LoRA configuration"""
        try:
            config_file = self.lora_path / "config.json"
            if config_file.exists():
                with open(config_file, "r") as f:
                    self.lora_config = json.load(f)
                print(f"📋 Loaded config: rank={self.lora_config.get('lora_rank', 4)}, alpha={self.lora_config.get('lora_alpha', 32)}")
            else:
                print("⚠️  No config.json found, using default values")
                self.lora_config = {"lora_rank": 4, "lora_alpha": 32}
        except Exception as e:
            print(f"⚠️  Error loading config: {e}, using defaults")
            self.lora_config = {"lora_rank": 4, "lora_alpha": 32}

    def load_lora_weights_fixed(self):
        """Load LoRA weights with proper handling"""
        print("🔄 Loading LoRA weights (fixed method)...")
        
        # Load config first
        self.load_config()
        
        # Try different file formats in order of preference
        lora_files = [
            ("pytorch_lora_weights.safetensors", "safetensors"),
            ("pytorch_lora_weights.bin", "torch"),
            ("lora_weights.pt", "torch")
        ]
        
        lora_weights = None
        used_file = None
        
        for filename, file_type in lora_files:
            lora_file = self.lora_path / filename
            if lora_file.exists():
                try:
                    print(f"📁 Found {filename}")
                    if file_type == "safetensors":
                        from safetensors.torch import load_file
                        lora_weights = load_file(lora_file, device="cpu")
                    else:
                        lora_weights = torch.load(lora_file, map_location="cpu")
                    used_file = filename
                    break
                except Exception as e:
                    print(f"❌ Failed to load {filename}: {e}")
                    continue
        
        if lora_weights is None:
            print("❌ No LoRA weights found!")
            print(f"📁 Available files in {self.lora_path}:")
            if self.lora_path.exists():
                for file in self.lora_path.iterdir():
                    print(f"  - {file.name}")
            raise RuntimeError("Could not load LoRA weights")
        
        print(f"✅ Loaded LoRA weights from {used_file}")
        print(f"📊 Found {len(lora_weights)} weight tensors")
        
        # Debug: Print some keys to understand the structure
        sample_keys = list(lora_weights.keys())[:10]
        print(f"🔍 Sample weight keys: {sample_keys}")
        
        # Apply LoRA weights using the pipeline's built-in method with proper adapter name
        try:
            # Method 1: Try using the pipeline's load_lora_weights with the weights dict
            print("🔄 Attempting to load LoRA using pipeline method...")
            
            # Create a temporary file to save the weights in the expected format
            temp_dir = self.lora_path / "temp_adapter"
            temp_dir.mkdir(exist_ok=True)
            
            # Save weights in the format expected by diffusers
            torch.save(lora_weights, temp_dir / "pytorch_lora_weights.bin")
            
            # Try loading with the pipeline
            self.pipeline.load_lora_weights(temp_dir, adapter_name="character_lora")
            
            # Set the adapter scale (strength)
            lora_scale = self.lora_config.get('lora_alpha', 32) / self.lora_config.get('lora_rank', 4)
            lora_scale = min(lora_scale / 32.0, 1.0)  # Normalize to reasonable range
            
            self.pipeline.set_adapters("character_lora", adapter_weights=[lora_scale])
            
            print(f"✅ Successfully loaded LoRA with scale {lora_scale:.3f}")
            
            # Clean up temp directory
            import shutil
            shutil.rmtree(temp_dir)
            
        except Exception as e:
            print(f"❌ Pipeline LoRA loading failed: {e}")
            print("🔄 Falling back to manual application...")
            
            # Fallback: Manual application
            self.apply_lora_manually(lora_weights)

    def apply_lora_manually(self, lora_weights: Dict):
        """Manually apply LoRA weights to the transformer"""
        print("🔧 Applying LoRA weights manually...")
        
        # Group weights by layer
        lora_layers = {}
        for key, weight in lora_weights.items():
            # Remove the .lora_A or .lora_B suffix to get the layer name
            if key.endswith('.lora_A') or key.endswith('.lora_B'):
                layer_name = key.rsplit('.', 1)[0]
                if layer_name not in lora_layers:
                    lora_layers[layer_name] = {}
                
                if key.endswith('.lora_A'):
                    lora_layers[layer_name]['A'] = weight
                elif key.endswith('.lora_B'):
                    lora_layers[layer_name]['B'] = weight

        print(f"🎯 Found {len(lora_layers)} LoRA layer pairs")

        # Apply LoRA to each layer
        applied_count = 0
        for layer_name, matrices in lora_layers.items():
            if 'A' in matrices and 'B' in matrices:
                try:
                    # Navigate to the module in the transformer
                    module = self.pipeline.transformer
                    
                    # Handle the layer path (remove 'transformer.' prefix if present)
                    path_parts = layer_name.replace('transformer.', '').split('.')
                    
                    for attr in path_parts:
                        if hasattr(module, attr):
                            module = getattr(module, attr)
                        else:
                            raise AttributeError(f"Module {module} has no attribute {attr}")

                    # Apply LoRA adaptation
                    if hasattr(module, 'weight'):
                        lora_A = matrices['A'].to(self.device, dtype=module.weight.dtype)
                        lora_B = matrices['B'].to(self.device, dtype=module.weight.dtype)

                        # Calculate LoRA delta weight
                        alpha = self.lora_config.get('lora_alpha', 32)
                        rank = self.lora_config.get('lora_rank', 4)
                        scaling = alpha / rank

                        delta_weight = (lora_B @ lora_A) * scaling

                        # Add to original weight
                        module.weight.data += delta_weight
                        applied_count += 1
                        
                        if applied_count <= 5:  # Print first few for debugging
                            print(f"  ✅ Applied LoRA to: {layer_name}")

                except Exception as e:
                    print(f"  ⚠️  Could not apply LoRA to layer {layer_name}: {e}")

        print(f"✅ Successfully applied LoRA to {applied_count} layers")

    def generate_image(
        self,
        prompt: str,
        negative_prompt: str = "blurry, low quality, distorted, deformed",
        width: int = 1024,
        height: int = 1024,
        num_inference_steps: int = 28,
        guidance_scale: float = 7.0,
        seed: Optional[int] = None
    ) -> Image.Image:
        """Generate image from prompt using SD3.5 pipeline"""

        # Set up generator for reproducibility
        generator = None
        if seed is not None:
            generator = torch.Generator(device=self.device).manual_seed(seed)

        print(f"🎨 Generating image with prompt: {prompt[:50]}...")

        # Generate image using the pipeline
        with torch.no_grad():
            result = self.pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                generator=generator,
                return_dict=True
            )

        return result.images[0]

    def test_character_consistency(self, character_name: str, output_dir: str, num_tests: int = 5):
        """Test character consistency with multiple prompts"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        test_prompts = [
            f"a photo of sks {character_name}, portrait, high quality, detailed",
            f"a photo of sks {character_name}, full body, standing, neutral background",
            f"a photo of sks {character_name}, close-up face, dramatic lighting",
            f"a photo of sks {character_name}, sitting, casual pose, indoor setting",
            f"a photo of sks {character_name}, action pose, dynamic scene"
        ]
        
        print(f"🧪 Testing character consistency for '{character_name}'")
        
        for i, prompt in enumerate(test_prompts[:num_tests]):
            print(f"  Test {i+1}/{num_tests}: {prompt}")
            
            try:
                image = self.generate_image(prompt, seed=42+i)  # Use different seeds
                
                filename = f"consistency_test_{i+1:02d}.png"
                image.save(output_path / filename)
                
                # Save prompt
                with open(output_path / f"prompt_{i+1:02d}.txt", "w") as f:
                    f.write(prompt)
                
                print(f"    ✅ Saved: {filename}")
                
            except Exception as e:
                print(f"    ❌ Error: {e}")

def main():
    parser = argparse.ArgumentParser(description="Fixed LoRA inference for FLUX.1-dev")
    parser.add_argument("--lora_path", type=str, required=True, help="Path to LoRA checkpoint")
    parser.add_argument("--model_id", type=str, default="black-forest-labs/FLUX.1-dev")
    parser.add_argument("--prompt", type=str, help="Single prompt to generate")
    parser.add_argument("--character", type=str, help="Character name for consistency testing")
    parser.add_argument("--output_dir", type=str, default="./fixed_test_results")
    parser.add_argument("--num_tests", type=int, default=5, help="Number of consistency tests")
    parser.add_argument("--steps", type=int, default=28)
    parser.add_argument("--guidance_scale", type=float, default=3.5)  # FLUX.1-dev uses lower guidance
    parser.add_argument("--width", type=int, default=1024)
    parser.add_argument("--height", type=int, default=1024)
    parser.add_argument("--seed", type=int, help="Random seed")
    parser.add_argument("--device", type=str, default="cuda")
    
    args = parser.parse_args()
    
    # Initialize inference
    try:
        inference = FixedLoRAInference(args.model_id, args.lora_path, args.device)
        
        if args.character:
            # Test character consistency
            inference.test_character_consistency(args.character, args.output_dir, args.num_tests)
            
        elif args.prompt:
            # Single prompt generation
            image = inference.generate_image(
                args.prompt,
                width=args.width,
                height=args.height,
                num_inference_steps=args.steps,
                guidance_scale=args.guidance_scale,
                seed=args.seed
            )
            
            output_path = Path(args.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            filename = "generated_fixed.png"
            image.save(output_path / filename)
            print(f"✅ Saved: {filename}")
            
        else:
            print("Please provide either --prompt or --character")
            
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
