#!/usr/bin/env python3
"""
Test script to verify FLUX.1-dev migration
This script tests the basic functionality without requiring actual training
"""

import torch
from transformers import CLIPTokenizer, T5TokenizerFast
from diffusers import FluxPipeline
import sys
from pathlib import Path

def test_flux_pipeline():
    """Test if FLUX.1-dev pipeline can be loaded"""
    print("🧪 Testing FLUX.1-dev pipeline loading...")
    
    try:
        # Test basic pipeline loading (without downloading the full model)
        model_id = "black-forest-labs/FLUX.1-dev"
        
        # Test tokenizer loading
        print("  📝 Testing tokenizers...")
        tokenizer = CLIPTokenizer.from_pretrained(model_id, subfolder="tokenizer")
        tokenizer_2 = T5TokenizerFast.from_pretrained(model_id, subfolder="tokenizer_2")
        
        print(f"  ✅ CLIP tokenizer loaded: vocab_size={tokenizer.vocab_size}")
        print(f"  ✅ T5 tokenizer loaded: vocab_size={tokenizer_2.vocab_size}")
        
        # Test basic tokenization
        test_prompt = "a photo of sks character1, portrait"
        
        clip_tokens = tokenizer(
            test_prompt,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )
        
        t5_tokens = tokenizer_2(
            test_prompt,
            padding="max_length",
            max_length=512,
            truncation=True,
            return_tensors="pt"
        )
        
        print(f"  ✅ CLIP tokenization: {clip_tokens.input_ids.shape}")
        print(f"  ✅ T5 tokenization: {t5_tokens.input_ids.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing FLUX.1-dev: {e}")
        return False

def test_dataset_compatibility():
    """Test if the dataset structure is compatible"""
    print("🧪 Testing dataset compatibility...")
    
    try:
        # Import our dataset class
        sys.path.append(str(Path(__file__).parent))
        from lora_training import MultiCharacterDataset
        
        # Test with dummy data structure
        test_data_root = Path("./training")
        if test_data_root.exists():
            print(f"  📁 Found training directory: {test_data_root}")
            
            # Count character folders
            char_folders = [d for d in test_data_root.iterdir() if d.is_dir()]
            print(f"  👥 Found {len(char_folders)} character folders")
            
            for char_folder in char_folders:
                images = list(char_folder.glob("*.jpg")) + list(char_folder.glob("*.png"))
                captions = list(char_folder.glob("*.txt"))
                print(f"    📸 {char_folder.name}: {len(images)} images, {len(captions)} captions")
        else:
            print("  ℹ️ No training directory found (this is OK for testing)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing dataset: {e}")
        return False

def test_imports():
    """Test if all required imports work"""
    print("🧪 Testing imports...")
    
    try:
        from diffusers import FluxTransformer2DModel, AutoencoderKL, FlowMatchEulerDiscreteScheduler
        from transformers import CLIPTextModel, T5EncoderModel
        print("  ✅ All diffusers imports successful")
        
        from accelerate import Accelerator
        print("  ✅ Accelerate import successful")
        
        import torch.nn.functional as F
        print("  ✅ PyTorch imports successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 FLUX.1-dev Migration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Dataset Compatibility", test_dataset_compatibility),
        ("FLUX.1-dev Pipeline", test_flux_pipeline),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! FLUX.1-dev migration is ready.")
        print("\n💡 Next steps:")
        print("  1. Run: python lora_training.py --data_root ./training --output_dir ./outputs_flux")
        print("  2. Test: python lora_inference_fixed.py --lora_path ./outputs_flux/checkpoint-final")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
        print("   Make sure you have the latest diffusers and transformers installed:")
        print("   pip install --upgrade diffusers transformers accelerate")

if __name__ == "__main__":
    main()
